package executor

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"arien-ai/pkg/types"
)

// ShellExecutor handles shell command execution with safety checks
type ShellExecutor struct {
	config types.SecurityConfig
}

// NewShellExecutor creates a new shell executor
func NewShellExecutor(config types.SecurityConfig) *ShellExecutor {
	return &ShellExecutor{
		config: config,
	}
}

// Execute executes a shell command with the given parameters
func (e *ShellExecutor) Execute(ctx context.Context, execution *types.CommandExecution) error {
	// Validate command
	if err := e.validateCommand(execution.Command); err != nil {
		execution.Success = false
		execution.Error = err.Error()
		return err
	}

	// Set default timeout if not specified
	if execution.Timeout == 0 {
		execution.Timeout = 30 * time.Second
	}

	// Apply maximum timeout limit
	if execution.Timeout > e.config.MaxCommandTimeout {
		execution.Timeout = e.config.MaxCommandTimeout
	}

	// Create context with timeout
	cmdCtx, cancel := context.WithTimeout(ctx, execution.Timeout)
	defer cancel()

	// Prepare command
	cmd, err := e.prepareCommand(cmdCtx, execution)
	if err != nil {
		execution.Success = false
		execution.Error = err.Error()
		return err
	}

	// Record start time
	startTime := time.Now()

	// Execute command
	if execution.CaptureOutput {
		err = e.executeWithOutput(cmd, execution)
	} else {
		err = cmd.Run()
	}

	// Record duration
	execution.Duration = time.Since(startTime)

	// Handle execution result
	if err != nil {
		execution.Success = false
		execution.Error = err.Error()
		
		// Extract exit code if available
		if exitError, ok := err.(*exec.ExitError); ok {
			execution.ExitCode = exitError.ExitCode()
		} else {
			execution.ExitCode = -1
		}
	} else {
		execution.Success = true
		execution.ExitCode = 0
	}

	return err
}

// validateCommand validates a command against security policies
func (e *ShellExecutor) validateCommand(command string) error {
	if command == "" {
		return fmt.Errorf("command cannot be empty")
	}

	// Check against blocked commands
	for _, blocked := range e.config.BlockedCommands {
		if strings.Contains(strings.ToLower(command), strings.ToLower(blocked)) {
			return fmt.Errorf("command contains blocked pattern: %s", blocked)
		}
	}

	// Check if command requires confirmation
	for _, requireConfirm := range e.config.RequireConfirm {
		if strings.HasPrefix(strings.ToLower(command), strings.ToLower(requireConfirm)) {
			return fmt.Errorf("command requires user confirmation: %s", requireConfirm)
		}
	}

	// In sandbox mode, only allow whitelisted commands
	if e.config.SandboxMode {
		allowed := false
		for _, allowedCmd := range e.config.AllowedCommands {
			if strings.HasPrefix(strings.ToLower(command), strings.ToLower(allowedCmd)) {
				allowed = true
				break
			}
		}
		if !allowed {
			return fmt.Errorf("command not allowed in sandbox mode: %s", command)
		}
	}

	return nil
}

// prepareCommand prepares the command for execution
func (e *ShellExecutor) prepareCommand(ctx context.Context, execution *types.CommandExecution) (*exec.Cmd, error) {
	var cmd *exec.Cmd

	// Determine shell and command format based on OS
	switch runtime.GOOS {
	case "windows":
		cmd = exec.CommandContext(ctx, "powershell", "-Command", execution.Command)
	default:
		cmd = exec.CommandContext(ctx, "sh", "-c", execution.Command)
	}

	// Set working directory
	if execution.WorkingDirectory != "" {
		// Validate working directory
		if err := e.validateWorkingDirectory(execution.WorkingDirectory); err != nil {
			return nil, err
		}
		cmd.Dir = execution.WorkingDirectory
	}

	// Set environment variables
	if len(execution.Environment) > 0 {
		cmd.Env = append(os.Environ(), execution.Environment...)
	}

	return cmd, nil
}

// validateWorkingDirectory validates the working directory
func (e *ShellExecutor) validateWorkingDirectory(dir string) error {
	// Convert to absolute path
	absDir, err := filepath.Abs(dir)
	if err != nil {
		return fmt.Errorf("invalid working directory: %w", err)
	}

	// Check if directory exists
	if _, err := os.Stat(absDir); os.IsNotExist(err) {
		return fmt.Errorf("working directory does not exist: %s", absDir)
	}

	// Check if it's actually a directory
	if info, err := os.Stat(absDir); err == nil && !info.IsDir() {
		return fmt.Errorf("working directory is not a directory: %s", absDir)
	}

	return nil
}

// executeWithOutput executes command and captures output
func (e *ShellExecutor) executeWithOutput(cmd *exec.Cmd, execution *types.CommandExecution) error {
	// Create pipes for stdout and stderr
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// Start command
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start command: %w", err)
	}

	// Read output
	stdoutData, err := readWithLimit(stdout, 1024*1024) // 1MB limit
	if err != nil {
		return fmt.Errorf("failed to read stdout: %w", err)
	}

	stderrData, err := readWithLimit(stderr, 1024*1024) // 1MB limit
	if err != nil {
		return fmt.Errorf("failed to read stderr: %w", err)
	}

	// Wait for command to complete
	err = cmd.Wait()

	// Set output
	execution.Stdout = string(stdoutData)
	execution.Stderr = string(stderrData)

	return err
}

// IsCommandSafe checks if a command is safe to execute
func (e *ShellExecutor) IsCommandSafe(command string) bool {
	return e.validateCommand(command) == nil
}

// RequiresConfirmation checks if a command requires user confirmation
func (e *ShellExecutor) RequiresConfirmation(command string) bool {
	for _, requireConfirm := range e.config.RequireConfirm {
		if strings.HasPrefix(strings.ToLower(command), strings.ToLower(requireConfirm)) {
			return true
		}
	}
	return false
}

// GetAllowedCommands returns the list of allowed commands
func (e *ShellExecutor) GetAllowedCommands() []string {
	return e.config.AllowedCommands
}

// GetBlockedCommands returns the list of blocked commands
func (e *ShellExecutor) GetBlockedCommands() []string {
	return e.config.BlockedCommands
}

// GetCommandsRequiringConfirmation returns commands that require confirmation
func (e *ShellExecutor) GetCommandsRequiringConfirmation() []string {
	return e.config.RequireConfirm
}

// IsSandboxMode returns whether sandbox mode is enabled
func (e *ShellExecutor) IsSandboxMode() bool {
	return e.config.SandboxMode
}

// UpdateSecurityConfig updates the security configuration
func (e *ShellExecutor) UpdateSecurityConfig(config types.SecurityConfig) {
	e.config = config
}

// GetWorkingDirectory returns the current working directory
func (e *ShellExecutor) GetWorkingDirectory() (string, error) {
	return os.Getwd()
}

// ChangeWorkingDirectory changes the current working directory
func (e *ShellExecutor) ChangeWorkingDirectory(dir string) error {
	if err := e.validateWorkingDirectory(dir); err != nil {
		return err
	}
	return os.Chdir(dir)
}

// GetEnvironmentVariable gets an environment variable
func (e *ShellExecutor) GetEnvironmentVariable(name string) string {
	return os.Getenv(name)
}

// SetEnvironmentVariable sets an environment variable
func (e *ShellExecutor) SetEnvironmentVariable(name, value string) error {
	return os.Setenv(name, value)
}

// ListEnvironmentVariables lists all environment variables
func (e *ShellExecutor) ListEnvironmentVariables() []string {
	return os.Environ()
}
