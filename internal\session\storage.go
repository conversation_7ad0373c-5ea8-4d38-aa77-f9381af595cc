package session

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"arien-ai/pkg/types"
)

// Storage handles persistent storage of sessions
type Storage struct {
	sessionDir string
}

// NewStorage creates a new storage instance
func NewStorage(sessionDir string) (*Storage, error) {
	// Expand home directory if needed
	if strings.HasPrefix(sessionDir, "~/") {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return nil, fmt.Errorf("failed to get user home directory: %w", err)
		}
		sessionDir = filepath.Join(homeDir, sessionDir[2:])
	}

	// Create session directory if it doesn't exist
	if err := os.MkdirAll(sessionDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create session directory: %w", err)
	}

	return &Storage{
		sessionDir: sessionDir,
	}, nil
}

// SaveSession saves a session to disk
func (s *Storage) SaveSession(session *types.Session) error {
	if session.ID == "" {
		return fmt.Errorf("session ID cannot be empty")
	}

	// Update last modified time
	session.LastModified = time.Now()

	// Create session file path
	filename := fmt.Sprintf("%s.json", session.ID)
	filepath := filepath.Join(s.sessionDir, filename)

	// Marshal session to JSON
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	// Write to file
	if err := os.WriteFile(filepath, data, 0644); err != nil {
		return fmt.Errorf("failed to write session file: %w", err)
	}

	return nil
}

// LoadSession loads a session from disk
func (s *Storage) LoadSession(sessionID string) (*types.Session, error) {
	if sessionID == "" {
		return nil, fmt.Errorf("session ID cannot be empty")
	}

	// Create session file path
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)

	// Check if file exists
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		return nil, fmt.Errorf("session not found: %s", sessionID)
	}

	// Read file
	data, err := os.ReadFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to read session file: %w", err)
	}

	// Unmarshal session
	var session types.Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to unmarshal session: %w", err)
	}

	return &session, nil
}

// DeleteSession deletes a session from disk
func (s *Storage) DeleteSession(sessionID string) error {
	if sessionID == "" {
		return fmt.Errorf("session ID cannot be empty")
	}

	// Create session file path
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)

	// Check if file exists
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	// Delete file
	if err := os.Remove(filepath); err != nil {
		return fmt.Errorf("failed to delete session file: %w", err)
	}

	return nil
}

// ListSessions returns a list of all saved sessions
func (s *Storage) ListSessions() ([]*types.SessionInfo, error) {
	// Read session directory
	entries, err := os.ReadDir(s.sessionDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read session directory: %w", err)
	}

	var sessions []*types.SessionInfo

	for _, entry := range entries {
		if entry.IsDir() || !strings.HasSuffix(entry.Name(), ".json") {
			continue
		}

		// Extract session ID from filename
		sessionID := strings.TrimSuffix(entry.Name(), ".json")

		// Get file info
		info, err := entry.Info()
		if err != nil {
			continue // Skip files we can't read
		}

		// Try to load session metadata
		session, err := s.LoadSession(sessionID)
		if err != nil {
			// If we can't load the session, create basic info from file
			sessions = append(sessions, &types.SessionInfo{
				ID:           sessionID,
				Title:        sessionID,
				CreatedAt:    info.ModTime(),
				LastModified: info.ModTime(),
				MessageCount: 0,
				Size:         info.Size(),
			})
			continue
		}

		// Create session info from loaded session
		sessionInfo := &types.SessionInfo{
			ID:           session.ID,
			Title:        session.Title,
			CreatedAt:    session.CreatedAt,
			LastModified: session.LastModified,
			MessageCount: len(session.Messages),
			TokenUsage:   session.TokenUsage,
			Size:         info.Size(),
		}

		sessions = append(sessions, sessionInfo)
	}

	// Sort sessions by last modified time (newest first)
	sort.Slice(sessions, func(i, j int) bool {
		return sessions[i].LastModified.After(sessions[j].LastModified)
	})

	return sessions, nil
}

// SessionExists checks if a session exists
func (s *Storage) SessionExists(sessionID string) bool {
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)
	_, err := os.Stat(filepath)
	return err == nil
}

// GetSessionSize returns the size of a session file in bytes
func (s *Storage) GetSessionSize(sessionID string) (int64, error) {
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)
	
	info, err := os.Stat(filepath)
	if err != nil {
		return 0, fmt.Errorf("failed to get session file info: %w", err)
	}
	
	return info.Size(), nil
}

// CleanupOldSessions removes sessions older than the specified duration
func (s *Storage) CleanupOldSessions(maxAge time.Duration) error {
	sessions, err := s.ListSessions()
	if err != nil {
		return fmt.Errorf("failed to list sessions: %w", err)
	}

	cutoff := time.Now().Add(-maxAge)
	var deletedCount int

	for _, session := range sessions {
		if session.LastModified.Before(cutoff) {
			if err := s.DeleteSession(session.ID); err != nil {
				// Log error but continue cleanup
				continue
			}
			deletedCount++
		}
	}

	return nil
}

// GetStorageStats returns statistics about session storage
func (s *Storage) GetStorageStats() (*types.StorageStats, error) {
	sessions, err := s.ListSessions()
	if err != nil {
		return nil, fmt.Errorf("failed to list sessions: %w", err)
	}

	stats := &types.StorageStats{
		TotalSessions: len(sessions),
		TotalSize:     0,
		OldestSession: time.Now(),
		NewestSession: time.Time{},
	}

	for _, session := range sessions {
		stats.TotalSize += session.Size
		
		if session.CreatedAt.Before(stats.OldestSession) {
			stats.OldestSession = session.CreatedAt
		}
		
		if session.CreatedAt.After(stats.NewestSession) {
			stats.NewestSession = session.CreatedAt
		}
	}

	// Calculate directory size
	err = filepath.WalkDir(s.sessionDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		if !d.IsDir() {
			info, err := d.Info()
			if err != nil {
				return err
			}
			stats.DirectorySize += info.Size()
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to calculate directory size: %w", err)
	}

	return stats, nil
}

// ExportSession exports a session to a specified file path
func (s *Storage) ExportSession(sessionID, exportPath string) error {
	session, err := s.LoadSession(sessionID)
	if err != nil {
		return fmt.Errorf("failed to load session: %w", err)
	}

	// Marshal session to JSON with indentation
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	// Write to export file
	if err := os.WriteFile(exportPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write export file: %w", err)
	}

	return nil
}

// ImportSession imports a session from a specified file path
func (s *Storage) ImportSession(importPath string) (*types.Session, error) {
	// Read import file
	data, err := os.ReadFile(importPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read import file: %w", err)
	}

	// Unmarshal session
	var session types.Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to unmarshal session: %w", err)
	}

	// Generate new ID if session already exists
	originalID := session.ID
	counter := 1
	for s.SessionExists(session.ID) {
		session.ID = fmt.Sprintf("%s_%d", originalID, counter)
		counter++
	}

	// Save imported session
	if err := s.SaveSession(&session); err != nil {
		return nil, fmt.Errorf("failed to save imported session: %w", err)
	}

	return &session, nil
}
