package utils

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"strings"
	"time"
)

// RetryConfig contains configuration for retry logic
type RetryConfig struct {
	MaxAttempts     int           // Maximum number of retry attempts
	InitialDelay    time.Duration // Initial delay between retries
	MaxDelay        time.Duration // Maximum delay between retries
	BackoffFactor   float64       // Exponential backoff factor
	Jitter          bool          // Add random jitter to delays
	RetryableErrors []string      // Error patterns that should trigger retries
}

// DefaultRetryConfig returns a sensible default retry configuration
func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		MaxAttempts:   3,
		InitialDelay:  1 * time.Second,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        true,
		RetryableErrors: []string{
			"timeout",
			"connection refused",
			"network unreachable",
			"temporary failure",
			"rate limit",
			"too many requests",
			"service unavailable",
			"internal server error",
		},
	}
}

// LLMRetryConfig returns retry configuration optimized for LLM API calls
func LLMRetryConfig() RetryConfig {
	return RetryConfig{
		MaxAttempts:   5,
		InitialDelay:  2 * time.Second,
		MaxDelay:      60 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        true,
		RetryableErrors: []string{
			"timeout",
			"rate limit",
			"too many requests",
			"service unavailable",
			"internal server error",
			"bad gateway",
			"gateway timeout",
			"connection reset",
			"temporary failure",
		},
	}
}

// RetryableFunc is a function that can be retried
type RetryableFunc func() error

// RetryWithResult is a function that returns a result and can be retried
type RetryWithResult[T any] func() (T, error)

// Retry executes a function with retry logic
func Retry(ctx context.Context, config RetryConfig, fn RetryableFunc) error {
	var lastErr error
	
	for attempt := 0; attempt < config.MaxAttempts; attempt++ {
		// Execute the function
		err := fn()
		if err == nil {
			return nil // Success
		}
		
		lastErr = err
		
		// Check if this is the last attempt
		if attempt == config.MaxAttempts-1 {
			break
		}
		
		// Check if the error is retryable
		if !isRetryableError(err, config.RetryableErrors) {
			return err // Non-retryable error
		}
		
		// Calculate delay for next attempt
		delay := calculateDelay(attempt, config)
		
		// Wait for the delay or context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}
	
	return fmt.Errorf("max retry attempts (%d) exceeded, last error: %w", config.MaxAttempts, lastErr)
}

// RetryWithResultFunc executes a function with retry logic and returns a result
func RetryWithResultFunc[T any](ctx context.Context, config RetryConfig, fn RetryWithResult[T]) (T, error) {
	var lastErr error
	var zeroValue T
	
	for attempt := 0; attempt < config.MaxAttempts; attempt++ {
		// Execute the function
		result, err := fn()
		if err == nil {
			return result, nil // Success
		}
		
		lastErr = err
		
		// Check if this is the last attempt
		if attempt == config.MaxAttempts-1 {
			break
		}
		
		// Check if the error is retryable
		if !isRetryableError(err, config.RetryableErrors) {
			return zeroValue, err // Non-retryable error
		}
		
		// Calculate delay for next attempt
		delay := calculateDelay(attempt, config)
		
		// Wait for the delay or context cancellation
		select {
		case <-ctx.Done():
			return zeroValue, ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}
	
	return zeroValue, fmt.Errorf("max retry attempts (%d) exceeded, last error: %w", config.MaxAttempts, lastErr)
}

// isRetryableError checks if an error should trigger a retry
func isRetryableError(err error, retryablePatterns []string) bool {
	if err == nil {
		return false
	}
	
	errorMsg := strings.ToLower(err.Error())
	
	for _, pattern := range retryablePatterns {
		if strings.Contains(errorMsg, strings.ToLower(pattern)) {
			return true
		}
	}
	
	return false
}

// calculateDelay calculates the delay for the next retry attempt
func calculateDelay(attempt int, config RetryConfig) time.Duration {
	// Calculate exponential backoff
	delay := float64(config.InitialDelay) * math.Pow(config.BackoffFactor, float64(attempt))
	
	// Apply maximum delay limit
	if delay > float64(config.MaxDelay) {
		delay = float64(config.MaxDelay)
	}
	
	// Add jitter if enabled
	if config.Jitter {
		jitter := rand.Float64() * 0.1 * delay // Up to 10% jitter
		delay += jitter
	}
	
	return time.Duration(delay)
}

// RetryableHTTPError checks if an HTTP status code is retryable
func RetryableHTTPError(statusCode int) bool {
	switch statusCode {
	case 408, // Request Timeout
		 429, // Too Many Requests
		 500, // Internal Server Error
		 502, // Bad Gateway
		 503, // Service Unavailable
		 504: // Gateway Timeout
		return true
	default:
		return false
	}
}

// WithRetry is a helper function that wraps a function with default retry logic
func WithRetry(ctx context.Context, fn RetryableFunc) error {
	return Retry(ctx, DefaultRetryConfig(), fn)
}

// WithLLMRetry is a helper function that wraps a function with LLM-optimized retry logic
func WithLLMRetry(ctx context.Context, fn RetryableFunc) error {
	return Retry(ctx, LLMRetryConfig(), fn)
}

// RetryStats contains statistics about retry attempts
type RetryStats struct {
	TotalAttempts    int
	SuccessfulAttempt int
	TotalDelay       time.Duration
	Errors           []error
}

// RetryWithStats executes a function with retry logic and returns statistics
func RetryWithStats(ctx context.Context, config RetryConfig, fn RetryableFunc) (RetryStats, error) {
	stats := RetryStats{
		Errors: make([]error, 0),
	}
	
	startTime := time.Now()
	
	for attempt := 0; attempt < config.MaxAttempts; attempt++ {
		stats.TotalAttempts++
		
		// Execute the function
		err := fn()
		if err == nil {
			stats.SuccessfulAttempt = attempt + 1
			stats.TotalDelay = time.Since(startTime)
			return stats, nil // Success
		}
		
		stats.Errors = append(stats.Errors, err)
		
		// Check if this is the last attempt
		if attempt == config.MaxAttempts-1 {
			break
		}
		
		// Check if the error is retryable
		if !isRetryableError(err, config.RetryableErrors) {
			stats.TotalDelay = time.Since(startTime)
			return stats, err // Non-retryable error
		}
		
		// Calculate delay for next attempt
		delay := calculateDelay(attempt, config)
		
		// Wait for the delay or context cancellation
		select {
		case <-ctx.Done():
			stats.TotalDelay = time.Since(startTime)
			return stats, ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}
	
	stats.TotalDelay = time.Since(startTime)
	lastErr := stats.Errors[len(stats.Errors)-1]
	return stats, fmt.Errorf("max retry attempts (%d) exceeded, last error: %w", config.MaxAttempts, lastErr)
}

// CircuitBreaker implements a simple circuit breaker pattern
type CircuitBreaker struct {
	maxFailures   int
	resetTimeout  time.Duration
	failures      int
	lastFailTime  time.Time
	state         CircuitState
}

// CircuitState represents the state of a circuit breaker
type CircuitState int

const (
	CircuitClosed CircuitState = iota
	CircuitOpen
	CircuitHalfOpen
)

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        CircuitClosed,
	}
}

// Execute executes a function through the circuit breaker
func (cb *CircuitBreaker) Execute(fn RetryableFunc) error {
	// Check if circuit should be reset
	if cb.state == CircuitOpen && time.Since(cb.lastFailTime) > cb.resetTimeout {
		cb.state = CircuitHalfOpen
		cb.failures = 0
	}
	
	// Reject if circuit is open
	if cb.state == CircuitOpen {
		return fmt.Errorf("circuit breaker is open")
	}
	
	// Execute function
	err := fn()
	
	if err != nil {
		cb.failures++
		cb.lastFailTime = time.Now()
		
		// Open circuit if max failures reached
		if cb.failures >= cb.maxFailures {
			cb.state = CircuitOpen
		}
		
		return err
	}
	
	// Success - reset circuit
	cb.failures = 0
	cb.state = CircuitClosed
	return nil
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) GetState() CircuitState {
	return cb.state
}

// Reset manually resets the circuit breaker
func (cb *CircuitBreaker) Reset() {
	cb.failures = 0
	cb.state = CircuitClosed
}
