package types

import (
	"encoding/json"
	"time"
)

// MessageRole represents the role of a message sender
type MessageRole string

const (
	RoleUser      MessageRole = "user"
	RoleAssistant MessageRole = "assistant"
	RoleSystem    MessageRole = "system"
	RoleTool      MessageRole = "tool"
)

// MessageType represents the type of message content
type MessageType string

const (
	MessageTypeUser      MessageType = "user"
	MessageTypeAssistant MessageType = "assistant"
	MessageTypeSystem    MessageType = "system"
	MessageTypeFunction  MessageType = "function"
	MessageTypeTool      MessageType = "tool"
	MessageTypeCommand   MessageType = "command"
	MessageTypeResult    MessageType = "result"
	MessageTypeError     MessageType = "error"
	MessageTypeThinking  MessageType = "thinking"

	// Legacy types for backward compatibility
	TypeText     MessageType = "text"
	TypeCommand  MessageType = "command"
	TypeResult   MessageType = "result"
	TypeError    MessageType = "error"
	TypeThinking MessageType = "thinking"
)

// Message represents a conversation message
type Message struct {
	ID           string            `json:"id"`
	Role         MessageRole       `json:"role"`
	Type         MessageType       `json:"type"`
	Content      string            `json:"content"`
	Metadata     map[string]any    `json:"metadata,omitempty"`
	Timestamp    time.Time         `json:"timestamp"`
	TokenUsage   *TokenUsage       `json:"token_usage,omitempty"`
	ToolCalls    []ToolCall        `json:"tool_calls,omitempty"`
	FunctionCall *FunctionCall     `json:"function_call,omitempty"` // Added for compatibility
	ToolResult   *ToolResult       `json:"tool_result,omitempty"`
}

// TokenUsage represents token usage statistics
type TokenUsage struct {
	InputTokens      int `json:"input_tokens"`
	OutputTokens     int `json:"output_tokens"`
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
	RequestCount     int `json:"request_count"`
}

// Add adds another TokenUsage to this one
func (t *TokenUsage) Add(other TokenUsage) {
	t.InputTokens += other.InputTokens
	t.OutputTokens += other.OutputTokens
	t.PromptTokens += other.PromptTokens
	t.CompletionTokens += other.CompletionTokens
	t.TotalTokens += other.TotalTokens
	t.RequestCount += other.RequestCount
}

// ToolCall represents a function call request
type ToolCall struct {
	ID       string          `json:"id"`
	Type     string          `json:"type"`
	Function FunctionCall    `json:"function"`
}

// FunctionCall represents a function call with parameters
type FunctionCall struct {
	Name      string          `json:"name"`
	Arguments json.RawMessage `json:"arguments"`
}

// ToolResult represents the result of a tool execution
type ToolResult struct {
	ToolCallID string `json:"tool_call_id"`
	Content    string `json:"content"`
	Success    bool   `json:"success"`
	Error      string `json:"error,omitempty"`
	ExitCode   int    `json:"exit_code,omitempty"`
	Duration   time.Duration `json:"duration,omitempty"`
}

// CommandExecution represents a shell command execution
type CommandExecution struct {
	Command          string        `json:"command"`
	WorkingDirectory string        `json:"working_directory"`
	Environment      []string      `json:"environment,omitempty"`
	Timeout          time.Duration `json:"timeout"`
	CaptureOutput    bool          `json:"capture_output"`
	RequireConfirm   bool          `json:"require_confirmation"`
	Stdout           string        `json:"stdout,omitempty"`
	Stderr           string        `json:"stderr,omitempty"`
	ExitCode         int           `json:"exit_code"`
	Duration         time.Duration `json:"duration"`
	Success          bool          `json:"success"`
	Error            string        `json:"error,omitempty"`
}

// NewMessage creates a new message with the given parameters
func NewMessage(role MessageRole, msgType MessageType, content string) *Message {
	return &Message{
		ID:        generateMessageID(),
		Role:      role,
		Type:      msgType,
		Content:   content,
		Metadata:  make(map[string]any),
		Timestamp: time.Now(),
	}
}

// NewUserMessage creates a new user message
func NewUserMessage(content string) *Message {
	return NewMessage(RoleUser, TypeText, content)
}

// NewAssistantMessage creates a new assistant message
func NewAssistantMessage(content string) *Message {
	return NewMessage(RoleAssistant, TypeText, content)
}

// NewSystemMessage creates a new system message
func NewSystemMessage(content string) *Message {
	return NewMessage(RoleSystem, TypeText, content)
}

// NewCommandMessage creates a new command execution message
func NewCommandMessage(command string) *Message {
	msg := NewMessage(RoleTool, TypeCommand, command)
	msg.Metadata["command"] = command
	return msg
}

// NewResultMessage creates a new command result message
func NewResultMessage(result string, success bool) *Message {
	msgType := TypeResult
	if !success {
		msgType = TypeError
	}
	msg := NewMessage(RoleTool, msgType, result)
	msg.Metadata["success"] = success
	return msg
}

// AddToolCall adds a tool call to the message
func (m *Message) AddToolCall(toolCall ToolCall) {
	if m.ToolCalls == nil {
		m.ToolCalls = make([]ToolCall, 0)
	}
	m.ToolCalls = append(m.ToolCalls, toolCall)
}

// SetToolResult sets the tool execution result
func (m *Message) SetToolResult(result *ToolResult) {
	m.ToolResult = result
}

// SetTokenUsage sets the token usage information
func (m *Message) SetTokenUsage(usage *TokenUsage) {
	m.TokenUsage = usage
}

// IsFromUser returns true if the message is from a user
func (m *Message) IsFromUser() bool {
	return m.Role == RoleUser
}

// IsFromAssistant returns true if the message is from the assistant
func (m *Message) IsFromAssistant() bool {
	return m.Role == RoleAssistant
}

// IsCommand returns true if the message represents a command execution
func (m *Message) IsCommand() bool {
	return m.Type == TypeCommand
}

// IsError returns true if the message represents an error
func (m *Message) IsError() bool {
	return m.Type == TypeError
}

// generateMessageID generates a unique message ID
func generateMessageID() string {
	return time.Now().Format("20060102150405") + "-" + randomMessageString(8)
}

// randomMessageString generates a random string of the given length for messages
func randomMessageString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
