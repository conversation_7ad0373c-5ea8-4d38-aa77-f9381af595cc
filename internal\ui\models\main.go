package models

import (
	"context"
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/config"
	"arien-ai/internal/executor"
	"arien-ai/internal/llm"
	"arien-ai/internal/session"
	"arien-ai/internal/ui/components"
	"arien-ai/internal/ui/styles"
	"arien-ai/internal/utils"
	"arien-ai/pkg/types"
)

// InteractiveOptions contains options for the interactive mode
type InteractiveOptions struct {
	SessionID        string
	ProviderOverride string
	ModelOverride    string
	SafeMode         bool
	NoHistory        bool
	DebugMode        bool
}

// ExitInfo contains information about the exit state
type ExitInfo struct {
	Message           string
	ShouldSaveSession bool
	HasErrors         bool
	SessionStats      *SessionStats
}

// SessionStats contains statistics about the session
type SessionStats struct {
	MessageCount int
	CommandCount int
	Duration     time.Duration
	TokenUsage   types.TokenUsage
}

// InputMode represents different input modes
type InputMode int

const (
	ModeNormal InputMode = iota
	ModeCommand
	ModeSearch
)

// SlashCommand represents a slash command
type SlashCommand struct {
	Name        string
	Description string
	Handler     func(*MainModel, []string) tea.Cmd
}

// AppMsg represents application-specific messages
type AppMsg interface {
	AppMsg()
}

// ThinkingStartMsg starts the thinking animation
type ThinkingStartMsg struct {
	Message string
}

func (ThinkingStartMsg) AppMsg() {}

// ThinkingStopMsg stops the thinking animation
type ThinkingStopMsg struct{}

func (ThinkingStopMsg) AppMsg() {}

// LLMResponseMsg contains an LLM response
type LLMResponseMsg struct {
	Message *types.Message
	Error   error
}

func (LLMResponseMsg) AppMsg() {}



// MainModel represents the main terminal interface model
type MainModel struct {
	configManager *config.Manager
	sessionManager *session.Manager
	llmClient     llm.Client
	executor      *executor.ShellExecutor
	theme         *styles.Theme
	options       InteractiveOptions

	// Current session
	session *types.Session

	// UI state
	width  int
	height int

	// UI Components
	header    *components.HeaderComponent
	history   *components.HistoryComponent
	input     *components.InputComponent
	thinking  *components.ThinkingComponent
	statusBar *components.StatusBarComponent

	// Application state
	started      time.Time
	exitInfo     ExitInfo
	currentMode  InputMode
	isThinking   bool
	lastError    error

	// Slash commands state
	showSlashMenu bool
	slashCommands []SlashCommand
	selectedSlash int
}

// NewMainModel creates a new main terminal model
func NewMainModel(configManager *config.Manager, options InteractiveOptions) (*MainModel, error) {
	config := configManager.Get()

	// Create session manager
	sessionManager, err := session.NewManager(
		configManager.GetSessionDirectory(),
		config.Session.MaxSessions,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create session manager: %w", err)
	}

	// Create LLM client
	llmClient, err := llm.NewClient(config.LLM)
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}

	// Create shell executor
	shellExecutor := executor.NewShellExecutor(config.Security)

	// Create or load session
	var currentSession *types.Session
	if options.SessionID != "" {
		currentSession, err = sessionManager.LoadSession(options.SessionID)
		if err != nil {
			return nil, fmt.Errorf("failed to load session: %w", err)
		}
	} else {
		currentSession = types.NewSession("Interactive Session", "CLI Terminal Session", config.LLM)
	}

	// Create theme
	theme := styles.GetTheme(config.UI.Theme)

	// Create UI components
	header := components.NewHeaderComponent(theme, currentSession, config)
	history := components.NewHistoryComponent(theme)
	input := components.NewInputComponent(theme)
	thinking := components.NewThinkingComponent(theme)
	statusBar := components.NewStatusBarComponent(theme, currentSession, config)

	// Set initial messages in history
	if len(currentSession.Messages) > 0 {
		history.SetMessages(currentSession.Messages)
	}

	// Create slash commands
	slashCommands := createSlashCommands()

	return &MainModel{
		configManager:  configManager,
		sessionManager: sessionManager,
		llmClient:      llmClient,
		executor:       shellExecutor,
		theme:          theme,
		options:        options,
		session:        currentSession,
		header:         header,
		history:        history,
		input:          input,
		thinking:       thinking,
		statusBar:      statusBar,
		started:        time.Now(),
		currentMode:    ModeNormal,
		slashCommands:  slashCommands,
	}, nil
}

// Init initializes the main model
func (m *MainModel) Init() tea.Cmd {
	return tea.Batch(
		m.input.Focus(),
		m.statusBar.SetMessage("Ready"),
	)
}

// Update handles messages and updates the model
func (m *MainModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.updateComponentSizes()
		return m, nil

	case tea.KeyMsg:
		return m.handleKeyMsg(msg)

	case ThinkingStartMsg:
		m.isThinking = true
		return m, m.thinking.Start(msg.Message)

	case ThinkingStopMsg:
		m.isThinking = false
		m.thinking.Stop()
		return m, nil

	case LLMResponseMsg:
		return m.handleLLMResponse(msg)

	case CommandExecutedMsg:
		return m.handleCommandExecuted(msg)
	}

	// Update components
	var cmd tea.Cmd

	m.input, cmd = m.input.Update(msg)
	cmds = append(cmds, cmd)

	cmd = m.history.Update(msg)
	cmds = append(cmds, cmd)

	cmd = m.thinking.Update(msg)
	cmds = append(cmds, cmd)

	return m, tea.Batch(cmds...)
}

// View renders the main terminal interface
func (m *MainModel) View() string {
	if m.width == 0 {
		return "Loading..."
	}

	// Calculate component heights
	headerHeight := m.header.GetHeight()
	statusHeight := m.statusBar.GetHeight()
	inputHeight := m.input.GetHeight()

	// Calculate available height for history
	historyHeight := m.height - headerHeight - statusHeight - inputHeight - 2 // 2 for margins
	if historyHeight < 5 {
		historyHeight = 5
	}

	// Update component sizes
	m.history.SetSize(m.width, historyHeight)

	// Render components
	header := m.header.Render()
	history := m.history.Render()
	input := m.input.Render()
	status := m.statusBar.Render()

	// Add thinking animation if active
	if m.isThinking && m.thinking.IsActive() {
		thinking := m.thinking.Render()
		history += "\n" + thinking
	}

	// Add slash menu if active
	if m.showSlashMenu {
		slashMenu := m.renderSlashMenu()
		input += "\n" + slashMenu
	}

	// Combine all components
	return lipgloss.JoinVertical(
		lipgloss.Left,
		header,
		history,
		input,
		status,
	)
}

// GetExitInfo returns information about the exit state
func (m *MainModel) GetExitInfo() ExitInfo {
	return m.exitInfo
}

// SaveCurrentSession saves the current session
func (m *MainModel) SaveCurrentSession() error {
	if m.sessionManager == nil || m.session == nil {
		return fmt.Errorf("session manager or session is nil")
	}
	return m.sessionManager.SaveSession(m.session)
}

// Placeholder implementations for missing methods


func (m *MainModel) handleCommandExecuted(msg CommandExecutedMsg) (tea.Model, tea.Cmd) {
	// TODO: Implement command execution result handling
	return m, nil
}

func (m *MainModel) executeToolCalls(toolCalls []types.ToolCall) tea.Cmd {
	// TODO: Implement tool call execution
	return nil
}



// updateComponentSizes updates the sizes of all UI components
func (m *MainModel) updateComponentSizes() {
	m.header.SetWidth(m.width)
	m.input.SetWidth(m.width)
	m.thinking.SetWidth(m.width)
	m.statusBar.SetWidth(m.width)
}

// handleKeyMsg handles keyboard input
func (m *MainModel) handleKeyMsg(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "ctrl+c":
		m.prepareExit()
		return m, tea.Quit

	case "enter":
		return m.handleEnterKey()

	case "/":
		if m.currentMode == ModeNormal && !m.input.HasText() {
			m.currentMode = ModeCommand
			m.showSlashMenu = true
			m.input.SetMode(components.ModeCommand)
			return m, nil
		}

	case "esc":
		if m.showSlashMenu {
			m.showSlashMenu = false
			m.currentMode = ModeNormal
			m.input.SetMode(components.ModeNormal)
			m.input.Clear()
			return m, nil
		}

	case "up", "down":
		if m.showSlashMenu {
			return m.handleSlashNavigation(msg.String())
		}
	}

	return m, nil
}

// handleEnterKey handles the enter key press
func (m *MainModel) handleEnterKey() (tea.Model, tea.Cmd) {
	inputValue := strings.TrimSpace(m.input.GetValue())
	if inputValue == "" {
		return m, nil
	}

	// Handle slash commands
	if m.currentMode == ModeCommand {
		return m.handleSlashCommand(inputValue)
	}

	// Handle normal message
	return m.handleUserMessage(inputValue)
}

// handleUserMessage processes a user message
func (m *MainModel) handleUserMessage(message string) (tea.Model, tea.Cmd) {
	// Clear input
	m.input.Clear()

	// Add user message to session
	userMsg := types.NewUserMessage(message)
	m.session.AddMessage(userMsg)
	m.history.AddMessage(userMsg)

	// Start thinking animation
	m.isThinking = true

	// Send to LLM
	return m, tea.Batch(
		func() tea.Msg { return ThinkingStartMsg{Message: "Processing..."} },
		m.sendToLLM(message),
	)
}

// sendToLLM sends a message to the LLM
func (m *MainModel) sendToLLM(message string) tea.Cmd {
	return func() tea.Msg {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Get recent messages for context
		messages := m.session.GetRecentMessages(10)

		// Get available tools
		tools := llm.GetAllTools()

		// Send to LLM with retry logic
		var response *types.Message
		var err error

		retryConfig := utils.LLMRetryConfig()
		err = utils.Retry(ctx, retryConfig, func() error {
			response, err = m.llmClient.Chat(ctx, messages, tools)
			return err
		})

		return LLMResponseMsg{
			Message: response,
			Error:   err,
		}
	}
}

// handleLLMResponse handles the LLM response
func (m *MainModel) handleLLMResponse(msg LLMResponseMsg) (tea.Model, tea.Cmd) {
	// Stop thinking animation
	m.isThinking = false
	m.thinking.Stop()

	if msg.Error != nil {
		// Handle error
		errorMsg := types.NewMessage(types.RoleAssistant, types.TypeError,
			fmt.Sprintf("Error: %v", msg.Error))
		m.session.AddMessage(errorMsg)
		m.history.AddMessage(errorMsg)
		m.lastError = msg.Error
		return m, nil
	}

	// Add assistant message to session
	m.session.AddMessage(msg.Message)
	m.history.AddMessage(msg.Message)

	// Handle tool calls if present
	if len(msg.Message.ToolCalls) > 0 {
		return m, m.executeToolCalls(msg.Message.ToolCalls)
	}

	return m, nil
}

// prepareExit prepares the exit information
func (m *MainModel) prepareExit() {
	duration := time.Since(m.started)

	stats := &SessionStats{
		MessageCount: len(m.session.Messages),
		CommandCount: len(m.session.CommandHistory),
		Duration:     duration,
		TokenUsage:   types.TokenUsage{
			TotalTokens: m.session.TokenUsage.TotalTokens,
		},
	}

	m.exitInfo = ExitInfo{
		Message:           "Goodbye! 👋",
		ShouldSaveSession: !m.options.NoHistory && len(m.session.Messages) > 0,
		HasErrors:         m.lastError != nil,
		SessionStats:      stats,
	}
}
